'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { cn } from '@/lib/utils'

interface FloatingAvatarProps {
  className?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl'
}

export function FloatingAvatar({ className, size = 'lg' }: FloatingAvatarProps) {
  const [isLoaded, setIsLoaded] = useState(false)

  // Size configurations
  const sizeConfig = {
    sm: {
      container: 'w-24 h-24',
      image: 'w-20 h-20',
      shadow: 'w-16 h-8',
      dimensions: 80
    },
    md: {
      container: 'w-32 h-32',
      image: 'w-28 h-28',
      shadow: 'w-20 h-10',
      dimensions: 112
    },
    lg: {
      container: 'w-48 h-48',
      image: 'w-44 h-44',
      shadow: 'w-32 h-16',
      dimensions: 176
    },
    xl: {
      container: 'w-64 h-64',
      image: 'w-60 h-60',
      shadow: 'w-40 h-20',
      dimensions: 240
    },
    '2xl': {
      container: 'w-80 h-80',
      image: 'w-76 h-76',
      shadow: 'w-48 h-24',
      dimensions: 304
    },
    '3xl': {
      container: 'w-96 h-96',
      image: 'w-92 h-92',
      shadow: 'w-56 h-28',
      dimensions: 368
    },
    '4xl': {
      container: 'w-112 h-112',
      image: 'w-108 h-108',
      shadow: 'w-64 h-32',
      dimensions: 432
    }
  }

  const config = sizeConfig[size]

  return (
    <div className={cn('relative flex items-center justify-center', config.container, className)}>
      {/* Floating animation container */}
      <div className="relative animate-float">
        {/* Main avatar image */}
        <div className="relative z-10">
          <Image
            src="/logo/chatbot_avatar_backgroundless.svg"
            alt="UpZera AI Assistant"
            width={config.dimensions}
            height={config.dimensions}
            className={cn(
              'transition-all duration-500 ease-out',
              config.image,
              isLoaded ? 'opacity-100 scale-100' : 'opacity-0 scale-95'
            )}
            onLoad={() => setIsLoaded(true)}
            priority
          />
          
          {/* Subtle glow effect around the avatar */}
          <div className="absolute inset-0 bg-gradient-radial from-purple-400/20 via-pink-400/10 to-transparent rounded-full blur-xl animate-pulse-slow" />
        </div>

        {/* Enhanced drop shadow beneath the avatar - multiple layers for realism */}
        {/* Main shadow */}
        <div
          className={cn(
            'absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-6',
            'bg-gradient-radial from-black/60 via-black/30 to-transparent',
            'rounded-full blur-lg',
            config.shadow,
            'animate-shadow-float'
          )}
        />

        {/* Inner shadow for depth */}
        <div
          className={cn(
            'absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-4',
            'bg-gradient-radial from-black/50 via-black/20 to-transparent',
            'rounded-full blur-md',
            size === '4xl' ? 'w-40 h-16' : size === '3xl' ? 'w-36 h-14' : size === '2xl' ? 'w-32 h-12' : size === 'xl' ? 'w-28 h-10' : size === 'lg' ? 'w-24 h-8' : size === 'md' ? 'w-16 h-6' : 'w-12 h-4',
            'animate-shadow-float'
          )}
        />

        {/* Outer shadow for soft edges */}
        <div
          className={cn(
            'absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-8',
            'bg-gradient-radial from-black/25 via-black/8 to-transparent',
            'rounded-full blur-xl',
            size === '4xl' ? 'w-72 h-24' : size === '3xl' ? 'w-64 h-22' : size === '2xl' ? 'w-56 h-20' : size === 'xl' ? 'w-48 h-18' : size === 'lg' ? 'w-40 h-16' : size === 'md' ? 'w-28 h-12' : 'w-20 h-8',
            'animate-shadow-float-slow'
          )}
        />
      </div>

      {/* Background ambient effects */}
      <div className="absolute inset-0 bg-gradient-radial from-purple-500/10 via-pink-500/5 to-transparent rounded-full blur-2xl animate-pulse-slow" />
      <div className="absolute -inset-4 bg-gradient-to-br from-purple-500/5 to-pink-500/5 rounded-full blur-3xl animate-pulse-slow" style={{ animationDelay: '1s' }} />
    </div>
  )
}

// CSS animations will be added to globals.css
